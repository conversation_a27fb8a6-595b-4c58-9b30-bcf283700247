"use client"

import React, { useState, useEffect, useMemo, useCallback } from "react"
import { formatDistanceToNow } from "date-fns"
import { Badge } from "@/components/ui/badge"
import { StatusEnum } from "@/lib/api/types"

const isXml = (str: string): boolean => {
  if (!str) return false;
  return /<[a-z][\s\S]*>/i.test(str);
};

const formatXml = (xml: string): string => {
  if (!xml) return '';
  
  try {
    //  Add line breaks and indentation to XML
    const formatted = xml
      .replace(/\s*<\?[^>]*\?>\s*/g, '\n$&\n') // Separate XML declaration
      .replace(/\s*<([^>]+)>/g, (_, p1) => { // Using _ to indicate unused parameter
        if (p1.startsWith('/')) {
          return `\n<${p1}>`; // Closing tag
        } else if (p1.endsWith('/')) {
          return `\n<${p1}>`; // Self-closing tag
        } else {
          return `\n<${p1}>`; // Opening tag
        }
      });

    // Simple indentation logic (for basic XML formatting)
    let indent = 0;
    const lines = formatted.split('\n');
    return lines.map(line => {
      line = line.trim();
      if (!line) return '';
      
      // Decrease indent for closing tags
      if (line.match(/^<\//) || line.match(/\/>$/)) {
        indent = Math.max(0, indent - 1);
      }
      
      const indentedLine = '  '.repeat(indent) + line;
      
      // Increase indent for opening tags (unless they're self-closing)
      if (line.match(/<[^/][^>]*[^/]>$/) && !line.endsWith('/>')) {
        indent++;
      }
      
      return indentedLine;
    }).filter(line => line !== '').join('\n');
  } catch (e) {
    console.error('Error formatting XML:', e);
    return xml; // Return original if formatting fails
  }
};


import { Button } from "@/components/ui/button"
import { Dialog, DialogClose, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { X, AlertCircle, CheckCircle2, Info, AlertTriangle, MessageSquare, Copy, Calendar, Tag, Star } from "lucide-react"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ScrollArea } from "@/components/ui/scroll-area"
import { getStatusInfo, getImpactInfo, getFrequencyInfo, getSystemInfo } from "@/lib/utils/issue-utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

interface IssueDetailsDialogProps {
  issue: MappedIssue | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onToggleFavorite: (issueId: number) => Promise<void>;
  isFavorite: boolean;
  isLoading?: boolean;
}

export interface MappedIssue {
  id: number;
  title: string;
  error_code?: string;
  error_message?: string;
  system_name: string;
  type?: string;
  status: string;
  impact?: string;
  frequency?: string;
  category?: string;
  description: string;
  createdBy: string;
  createdAt: string;
  jira_id?: string ;
  jira_link?: string ;
  hemants_view?: string ;
  solution: {
    exists: boolean;
    content?: string;
    verified?: boolean;
    author?: string;
    createdAt?: string;
  };
}

// Error Boundary Component
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }


  override render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 bg-red-50 dark:bg-red-900/10 text-red-700 dark:text-red-300 rounded-md">
          <h3 className="font-medium">Something went wrong</h3>
          <p className="text-sm">Please try again later.</p>
        </div>
      );
    }

    return this.props.children;
  }
}

export const IssueDetailsDialog = ({
  issue,
  open,
  onOpenChange,
  onToggleFavorite,
  isFavorite,
}: IssueDetailsDialogProps) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("details");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Format date to relative time (e.g., "2 days ago")
  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'N/A';
    
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? 'Invalid date' : formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  }

  // Copy error code to clipboard
  const copyErrorCode = useCallback((): void => {
    if (!issue?.error_code) {
      toast({
        title: "No error code to copy",
        variant: "destructive",
        duration: 2000,
      });
      return;
    }
    
    navigator.clipboard.writeText(issue.error_code)
      .then(() => {
        toast({
          title: "Copied to clipboard",
          description: `Error code ${issue?.error_code} copied to clipboard`,
          duration: 2000,
        });
      })
      .catch((err) => {
        console.error('Failed to copy text:', err);
        toast({
          title: "Failed to copy",
          description: "Could not copy error code to clipboard",
          variant: "destructive",
          duration: 2000,
        });
      });
  }, [issue?.error_code, toast]);

  // Handle dialog open/close and issue loading
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (open) {
      if (issue) {
        try {
          setIsLoading(true);
          setError(null);
          
          // Simulate loading with a small delay for better UX
          timer = setTimeout(() => {
            setIsLoading(false);
          }, 100);
        } catch (err) {
          console.error('Error in dialog open effect:', err);
          setError('Failed to load issue details');
          setIsLoading(false);
        }
      } else {
        setError('No issue data available');
        setIsLoading(false);
      }
    } else {
      // Reset state when dialog is closed
      setIsLoading(false);
      setError(null);
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [open, issue]);
  

  const statusInfo = useMemo(() => {
    if (!issue?.status) return null;
    return getStatusInfo(issue.status as StatusEnum);
  }, [issue?.status]);

  // Default status info for when status is not available
  const defaultStatusInfo = {
    color: 'text-gray-700 dark:text-gray-400',
    bgColor: 'bg-gray-50 dark:bg-gray-900/60',
    ringColor: 'ring-gray-200 dark:ring-gray-800',
    icon: React.createElement(Info, { className: 'h-4 w-4' }),
  };

  const displayStatusInfo = statusInfo || defaultStatusInfo;

  const impactInfo = useMemo(() => 
    issue?.impact ? getImpactInfo(issue.impact) : null  ,
    [issue?.impact]
  );

  // Handle opening JIRA link in a new tab
  const handleOpenJira = useCallback(() => {
    if (issue?.jira_link) {
      window.open(issue.jira_link, '_blank', 'noopener,noreferrer');
      return true; // Indicate success
    }
    return false; // Indicate failure
  }, [issue?.jira_link]);

  const handleToggleFavorite = async () => {
    if (!issue) return;
    
    try {
      setIsLoading(true);
      await onToggleFavorite(issue.id);
      toast({
        title: isFavorite ? "Removed from favorites" : "Added to favorites",
        variant: "default",
        duration: 2000,
      });
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast({
        title: "Failed to update favorites",
        variant: "destructive",
        duration: 2000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!issue ) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent aria-describedby="no-issue-desc">
          <DialogHeader>
            <DialogTitle>No Issue Selected</DialogTitle>
          </DialogHeader>
          <p id="no-issue-desc" className="text-sm text-muted-foreground">
            Please select an issue to view its details.
          </p>
        </DialogContent>
      </Dialog>
    );
  }
  
  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent aria-describedby="loading-desc">
          <DialogHeader>
            <DialogTitle>Loading Issue</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center p-8">
            <div 
              aria-live="polite"
              aria-atomic="true"
              className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
            ></div>
            <span id="loading-desc" className="sr-only">Loading issue details, please wait...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }
  
  if (error) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent aria-describedby="error-desc">
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
          </DialogHeader>
          <div className="p-4 bg-red-50 dark:bg-red-900/10 text-red-700 dark:text-red-300 rounded-md">
            <p id="error-desc" className="text-sm">{error}</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Get initials for avatar
  const getInitials = (name: string | undefined): string => {
    if (!name) return "??";
    return name
      .split(" ")
      .map((part) => part[0]?.toUpperCase() || '')
      .join("")
      .substring(0, 2);
  }

  return (
    <ErrorBoundary>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent 
          className="max-w-4xl w-[95vw] h-[90vh] p-0 flex flex-col"
          aria-describedby="issue-details-desc"
        >
          <DialogHeader className="sr-only">
            <DialogTitle>Issue Details</DialogTitle>
          </DialogHeader>
          {/* Header Section */}
          <div className="border-b border-slate-200 dark:border-slate-800">
            <div className="flex items-center justify-between p-4">
              <div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="font-mono text-xs">
                    #{issue.id}
                  </Badge>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className={cn(
                            "h-6 px-2 text-xs font-mono flex items-center gap-1.5",
                            !issue.error_code && "text-muted-foreground"
                          )}
                          onClick={copyErrorCode}
                          disabled={!issue.error_code}
                        >
                          {issue.error_code || "No Error Code"}
                          {issue.error_code && <Copy className="h-3 w-3" />}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {issue.error_code ? "Copy error code" : "No error code available"}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <h2 className="mt-2 text-xl font-bold tracking-tight">{issue.title}</h2>
              </div>
              
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleToggleFavorite}
                        className="h-9 w-9"
                        aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
                      >
                        <Star
                          className={`h-5 w-5 ${isFavorite ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"}`}
                        />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{isFavorite ? "Remove from favorites" : "Add to favorites"}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <DialogClose asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-9 w-9"
                    aria-label="Close dialog"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </DialogClose>
              </div>
            </div>

            {/* Metadata Tags */}
            <div className="px-4 pb-4 flex flex-wrap gap-2">
              {/* Status */}
              {issue.status && issue.status !== 'UNKNOWN' && (
                <Badge  
                  variant="outline"
                  className={cn(
                    "flex items-center gap-1.5",
                    displayStatusInfo.bgColor,
                    displayStatusInfo.color
                  )}
                >
                  {displayStatusInfo.icon}
                  {issue.status.split('_').map((word: string) => word.charAt(0) + word.slice(1).toLowerCase()).join(' ')}
                </Badge>
              )}

              {/* Impact */}
              {issue.impact && (
                <Badge
                  variant="outline"
                  className={cn(
                    "flex items-center gap-1.5",
                    impactInfo?.color || "text-gray-600",
                    impactInfo?.color || "text-gray-600"
                  )}
                >
                  {issue.impact === 'High' ? (
                    <AlertCircle className="h-3.5 w-3.5" />
                  ) : issue.impact === 'Medium' ? (
                    <AlertTriangle className="h-3.5 w-3.5" />
                  ) : (
                    <Info className="h-3.5 w-3.5" />
                  )}
                  {issue.impact}
                </Badge>
              )}

              {/* Frequency */}
              {issue.frequency && (
                <Badge
                  variant="outline"
                  className={cn(
                    "flex items-center gap-1.5 font-normal",
                    issue.frequency !== 'UNKNOWN' ? getFrequencyInfo(issue.frequency).bgColor : "bg-gray-50 dark:bg-gray-900/60",
                    issue.frequency !== 'UNKNOWN' ? getFrequencyInfo(issue.frequency).color : "text-gray-600 dark:text-gray-400"
                  )}
                >
                  {issue.frequency !== 'UNKNOWN' ? getFrequencyInfo(issue.frequency).icon : <Info className="h-3.5 w-3.5" />}
                  {issue.frequency}
                </Badge>
              )}

              {/* Category */}
              {issue.category && (
                <Badge variant="outline" className="font-normal">
                  {issue.category.split('_').map((word: string) => word.charAt(0) + word.slice(1).toLowerCase()).join(' ')}
                </Badge>
              )}

              {/* System Name */}
              {issue.system_name && (
                <Badge
                  variant="outline"
                  className={cn(
                    "flex items-center gap-1.5 font-normal",
                    issue.system_name !== 'OTHERS' ? getSystemInfo(issue.system_name).bgColor : "bg-gray-50 dark:bg-gray-900/60",
                    issue.system_name !== 'OTHERS' ? getSystemInfo(issue.system_name).color : "text-gray-600 dark:text-gray-400"
                  )}
                >
                  {issue.system_name !== 'OTHERS' ? getSystemInfo(issue.system_name).icon : <Info className="h-3.5 w-3.5" />}
                  {issue.system_name}
                </Badge>
              )}
            </div>

            {/* Secondary Metadata */}
            <div className="px-4 py-2 border-t border-slate-200 dark:border-slate-800 bg-slate-50 dark:bg-slate-900/30 flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Avatar className="h-5 w-5">
                  <AvatarFallback className="text-xs">
                    {getInitials(issue.createdBy || 'System')}
                  </AvatarFallback>
                </Avatar>
                <span>{issue.createdBy || 'System'}</span>
              </div>
              
              <div className="flex items-center gap-1.5">
                <Calendar className="h-3.5 w-3.5" />
                <span>{formatDate(issue.createdAt)}</span>
              </div>
              
              <div className="flex items-center gap-1.5">
                <span className="text-xs font-medium">JIRA:</span>
                {issue.jira_id ? (
                  issue.jira_link ? (
                    <a
                      href={issue.jira_link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:underline text-xs"
                    >
                      {issue.jira_id}
                    </a>
                  ) : (
                    <span className="text-xs">{issue.jira_id}</span>
                  )
                ) : (
                  <span className="text-xs text-muted-foreground">Not linked</span>
                )}
              </div>
            </div>
          </div>

        {/* Main Content Area with Tabs */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs 
            defaultValue="details" 
            value={activeTab} 
            onValueChange={setActiveTab} 
            className="flex-1 flex flex-col h-full"
          >
            {/* Tabs Navigation */}
            <div className="border-b border-slate-200 dark:border-slate-800 px-4">
              <TabsList className="w-full justify-start bg-transparent p-0 h-10">
                <TabsTrigger 
                  value="details" 
                  className="px-4 py-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                >
                  Details
                </TabsTrigger>
                <TabsTrigger 
                  value="solution" 
                  className="px-4 py-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                >
                  Solution
                  {issue.solution?.exists && (
                    <span className="ml-1.5 h-2 w-2 rounded-full bg-emerald-500 inline-block"></span>
                  )}
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full w-full">
                <div className="p-6 space-y-6">
                  {/* Details Tab */}
                  <TabsContent value="details" className="m-0 space-y-6">
                    {/* Description Card */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base font-medium">
                          Description
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="prose prose-sm dark:prose-invert max-w-none">
                          <p className="whitespace-pre-line">{issue.description}</p>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Error Message Card */}
                    {issue.error_message && (
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base font-medium">
                            Error Message
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="relative group">
                            <pre className="bg-slate-50 dark:bg-slate-900/50 p-4 rounded-md text-xs overflow-x-auto border border-slate-200 dark:border-slate-700 font-mono">
                              <code className="text-slate-800 dark:text-slate-200">
                                {isXml(issue.error_message) ? formatXml(issue.error_message) : issue.error_message}
                              </code>
                            </pre>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="absolute top-2 right-2 h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity disabled:opacity-50 disabled:pointer-events-none"
                              onClick={() => {
                                if (issue.error_message) {
                                  navigator.clipboard.writeText(issue.error_message);
                                  toast({
                                    title: "Copied to clipboard",
                                    description: "Error message copied to clipboard",
                                    duration: 2000,
                                  });
                                }
                              }}
                              disabled={!issue.error_message}
                              title={!issue.error_message ? 'No error message to copy' : 'Copy error message'}
                            >
                              <Copy className="h-4 w-4" />
                              <span className="sr-only">Copy error message</span>
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Additional Details Card */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base font-medium">
                          Additional Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-muted-foreground">Type:</span>
                            <span className="ml-2">{issue.type || 'Not specified'}</span>
                          </div>
                          <div>
                            <span className="font-medium text-muted-foreground">Impact:</span>
                            <span className="ml-2">{issue.impact || 'Not specified'}</span>
                          </div>
                          <div>
                            <span className="font-medium text-muted-foreground">Frequency:</span>
                            <span className="ml-2">{issue.frequency || 'Not specified'}</span>
                          </div>
                          <div>
                            <span className="font-medium text-muted-foreground">System:</span>
                            <span className="ml-2">{issue.system_name || 'Not specified'}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Client Feedback Card */}
                    <Card className={cn(
                      "border-blue-200 dark:border-blue-900/50",
                      !issue.hemants_view && "opacity-60"
                    )}>
                      <CardHeader className="pb-2 bg-blue-50/50 dark:bg-blue-900/10">
                        <CardTitle className="text-base font-medium flex items-center gap-2 text-blue-900 dark:text-blue-100">
                          <MessageSquare className="h-4 w-4" />
                          Client Feedback
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-4">
                        <div className="prose prose-sm dark:prose-invert max-w-none">
                          {issue.hemants_view ? (
                            <p className="whitespace-pre-line">{issue.hemants_view}</p>
                          ) : (
                            <p className="text-muted-foreground italic">No client feedback provided</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Solution Tab */}
                  <TabsContent value="solution" className="m-0">
                    {issue.solution?.exists ? (
                      <Card>
                        <CardHeader className="pb-2 bg-emerald-50/50 dark:bg-emerald-900/10">
                          <CardTitle className="text-base font-medium flex items-center gap-2 text-emerald-900 dark:text-emerald-100">
                            <CheckCircle2 className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                            Solution
                            {issue.solution.verified && (
                              <Badge 
                                variant="secondary" 
                                className="ml-2 bg-emerald-100 text-emerald-700 dark:bg-emerald-900/40 dark:text-emerald-300 hover:bg-emerald-100 dark:hover:bg-emerald-900/40"
                              >
                                Verified
                              </Badge>
                            )}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-4">
                          <div className="prose prose-sm dark:prose-invert max-w-none">
                            {issue.solution.content?.trim() ? (
                              <p className="whitespace-pre-line">{issue.solution.content}</p>
                            ) : (
                              <p className="text-muted-foreground italic">No solution content available</p>
                            )}
                          </div>
                        </CardContent>
                        <CardFooter className="flex flex-wrap items-center justify-between text-sm text-muted-foreground pt-4 pb-4 border-t">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-5 w-5">
                              <AvatarFallback className="text-xs">
                                {issue.solution.author ? getInitials(issue.solution.author) : "?"}
                              </AvatarFallback>
                            </Avatar>
                            <span>Provided by: {issue.solution.author || 'Unknown'}</span>
                          </div>
                          {issue.solution.createdAt && (
                            <div className="flex items-center gap-1.5 text-xs">
                              <Calendar className="h-3.5 w-3.5" />
                              <span>{formatDate(issue.solution.createdAt)}</span>
                            </div>
                          )}
                        </CardFooter>
                      </Card>
                    ) : (
                      <Card className="flex flex-col items-center justify-center py-12 text-center">
                        <div className="rounded-full bg-slate-100 dark:bg-slate-800 p-4 mb-4">
                          <AlertCircle className="h-8 w-8 text-slate-400" />
                        </div>
                        <CardTitle className="text-lg font-medium mb-2">No solution yet</CardTitle>
                        <p className="text-muted-foreground max-w-md">
                          This issue doesn't have a solution yet. Solutions help other team members resolve similar issues in the future.
                        </p>
                      </Card>
                    )}
                  </TabsContent>
                </div>
              </ScrollArea>
            </div>
          </Tabs>
        </div>
        </DialogContent>
      </Dialog>
    </ErrorBoundary>
  )
}