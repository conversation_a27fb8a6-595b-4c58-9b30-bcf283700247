from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from backend.database import get_db
from backend.models import User, KnowledgeBase, CategoryEnum, KnowledgeBaseFavorite, Solution
from backend.dependencies import get_current_user
from pydantic import BaseModel
from datetime import datetime

router = APIRouter(
    prefix="/knowledge-base",
    tags=["knowledge-base"],
)

class KnowledgeBaseCreate(BaseModel):
    project_id: int
    title: str
    content: str
    solution_id: Optional[int] = None
    file_url: Optional[str] = None

class KnowledgeBaseResponse(BaseModel):
    kb_id: int
    project_id: int
    title: str
    content: str
    solution_id: Optional[int] = None
    solution: Optional[str] = None
    uploaded_by: int
    file_url: Optional[str] = None
    created_at: datetime
    author_name: Optional[str] = None
    verified: Optional[bool] = False
    views: Optional[int] = 0
    likes: Optional[int] = 0
    system: Optional[str] = None
    type: Optional[str] = None
    is_favorite: Optional[bool] = False

    class Config:
        from_attributes = True

class KnowledgeBaseListResponse(BaseModel):
    items: List[KnowledgeBaseResponse]
    total: int

@router.post("", response_model=KnowledgeBaseResponse)
async def create_knowledge_base_entry(
    entry: KnowledgeBaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new knowledge base entry"""
    try:
        # Check if the solution exists if solution_id is provided
        if entry.solution_id is not None:
            solution = db.query(Solution).filter(Solution.solution_id == entry.solution_id).first()
            if not solution:
                raise HTTPException(status_code=400, detail=f"Solution with ID {entry.solution_id} not found")

        # Create the knowledge base entry
        db_entry = KnowledgeBase(
            project_id=entry.project_id,
            title=entry.title,
            content=entry.content,
            solution_id=entry.solution_id,
            file_url=entry.file_url,
            uploaded_by=current_user.user_id
        )

        db.add(db_entry)
        db.commit()
        db.refresh(db_entry)

        # Add author name to response
        response = KnowledgeBaseResponse.from_orm(db_entry)
        response.author_name = current_user.name

        return response
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@router.get("", response_model=KnowledgeBaseListResponse)
async def get_knowledge_base_entries(
    filter: str = Query("all", description="Filter type: all, verified, community, my-contributions, favorites"),
    category: str = Query("all", description="Category filter: all, solutions, guides, faq"),
    project_id: int = Query(None, description="Filter by project ID"),
    search: Optional[str] = Query(None, description="Search query"),
    limit: Optional[int] = Query(None, description="Limit number of results"),
    sort_by: Optional[str] = Query(None, description="Sort by field (e.g., 'created_at')"),
    sort_order: Optional[str] = Query("desc", description="Sort order: 'asc' or 'desc'"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get knowledge base entries with filters"""
    try:
        # Start with base query
        query = db.query(KnowledgeBase)

        # Apply project filter if provided
        if project_id is not None:
            query = query.filter(KnowledgeBase.project_id == project_id)

        # Apply search filter if provided
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (KnowledgeBase.title.ilike(search_term)) |
                (KnowledgeBase.content.ilike(search_term))
            )

        # Apply filter based on type
        if filter == "my-contributions":
            query = query.filter(KnowledgeBase.uploaded_by == current_user.user_id)
        elif filter == "favorites":
            # Join with favorites table to get only favorited entries
            query = query.join(
                KnowledgeBaseFavorite,
                KnowledgeBase.kb_id == KnowledgeBaseFavorite.kb_id
            ).filter(KnowledgeBaseFavorite.user_id == current_user.user_id)

        # Apply sorting
        if sort_by:
            sort_column = getattr(KnowledgeBase, sort_by, None)
            if sort_column is not None:
                if sort_order == "asc":
                    query = query.order_by(sort_column.asc())
                else:
                    query = query.order_by(sort_column.desc())
        else:
            # Default: sort by created_at descending
            query = query.order_by(KnowledgeBase.created_at.desc())

        # Apply limit
        if limit:
            query = query.limit(limit)

        # Execute query
        entries = query.all()

        # Process entries to add additional fields
        result_entries = []
        for entry in entries:
            # Get author name
            author = db.query(User).filter(User.user_id == entry.uploaded_by).first()
            author_name = author.name if author else None

            # Create response object
            kb_response = KnowledgeBaseResponse.from_orm(entry)
            kb_response.author_name = author_name

            # Check if entry is favorited by current user
            is_favorite = db.query(KnowledgeBaseFavorite).filter(
                KnowledgeBaseFavorite.kb_id == entry.kb_id,
                KnowledgeBaseFavorite.user_id == current_user.user_id
            ).first() is not None

            # Add favorite status
            kb_response.is_favorite = is_favorite

            # Add mock system and type
            systems = ["OMS", "WMS", "Automation", "General"]
            kb_response.system = systems[entry.kb_id % len(systems)]
            types = ["Database", "API", "UI", "Workflow", "Documentation"]
            kb_response.type = types[entry.kb_id % len(types)]

            # Include solution information if available
            if entry.solution_id:
                solution = db.query(Solution).filter(Solution.solution_id == entry.solution_id).first()
                if solution:
                    kb_response.solution = solution.solution_text

            result_entries.append(kb_response)

        return {"items": result_entries, "total": len(result_entries)}
    except Exception as e:
        # Log the error for debugging
        import logging
        logging.error(f"Error fetching knowledge entries: {str(e)}")

        # Return empty result instead of failing
        return {"items": [], "total": 0}

@router.get("/{kb_id}", response_model=KnowledgeBaseResponse)
async def get_knowledge_base_entry(
    kb_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific knowledge base entry by ID"""
    try:
        entry = db.query(KnowledgeBase).filter(KnowledgeBase.kb_id == kb_id).first()
        if not entry:
            raise HTTPException(status_code=404, detail="Knowledge base entry not found")

        # Get author name
        author = db.query(User).filter(User.user_id == entry.uploaded_by).first()
        author_name = author.name if author else None

        # Create response object
        response = KnowledgeBaseResponse.from_orm(entry)
        response.author_name = author_name

        # Add mock fields for now
        response.verified = entry.kb_id % 2 == 0
        response.views = entry.kb_id * 10
        # Set likes to 0 - this will be managed client-side with localStorage
        response.likes = 0

        # Add mock system and type
        systems = ["OMS", "WMS", "Automation", "Other"]
        response.system = systems[entry.kb_id % len(systems)]
        types = ["Database", "API", "UI", "Workflow", "Documentation"]
        response.type = types[entry.kb_id % len(types)]

        # Check if entry is favorited by current user
        is_favorite = db.query(KnowledgeBaseFavorite).filter(
            KnowledgeBaseFavorite.kb_id == entry.kb_id,
            KnowledgeBaseFavorite.user_id == current_user.user_id
        ).first() is not None
        response.is_favorite = is_favorite

        # Include solution information if available
        if entry.solution_id:
            solution = db.query(Solution).filter(Solution.solution_id == entry.solution_id).first()
            if solution:
                response.solution = solution.solution_text

        return response
    except HTTPException:
        raise
    except Exception as e:
        # Log the error for debugging
        import logging
        logging.error(f"Error fetching knowledge entry: {str(e)}")

        # Create a mock response instead of failing
        mock_response = KnowledgeBaseResponse(
            kb_id=kb_id,
            project_id=1,
            title="Error retrieving entry",
            content="There was an error retrieving this knowledge base entry.",
            solution=None,
            uploaded_by=1,
            created_at=datetime.now(),
            author_name="System",
            verified=False,
            views=0,
            likes=0,
            system="Other",
            type="Documentation",
            is_favorite=False
        )
        return mock_response

@router.post("/{kb_id}/like", status_code=status.HTTP_204_NO_CONTENT)
async def like_knowledge_base_entry(
    kb_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Like a knowledge base entry"""
    try:
        # Just check if the entry exists
        entry = db.query(KnowledgeBase).filter(KnowledgeBase.kb_id == kb_id).first()
        if not entry:
            raise HTTPException(status_code=404, detail="Knowledge base entry not found")

        # We'll use client-side storage for likes, so just return success
        # The actual like state will be managed in the frontend using localStorage
        return None
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{kb_id}/favorite", status_code=status.HTTP_204_NO_CONTENT)
async def toggle_favorite_knowledge_base_entry(
    kb_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Toggle favorite status for a knowledge base entry"""
    try:
        # Check if entry exists
        entry = db.query(KnowledgeBase).filter(KnowledgeBase.kb_id == kb_id).first()
        if not entry:
            raise HTTPException(status_code=404, detail="Knowledge base entry not found")

        # Check if already favorited
        existing_favorite = db.query(KnowledgeBaseFavorite).filter(
            KnowledgeBaseFavorite.kb_id == kb_id,
            KnowledgeBaseFavorite.user_id == current_user.user_id
        ).first()

        if existing_favorite:
            # Remove favorite if it exists
            db.delete(existing_favorite)
        else:
            # Add new favorite
            new_favorite = KnowledgeBaseFavorite(
                kb_id=kb_id,
                user_id=current_user.user_id
            )
            db.add(new_favorite)

        db.commit()
        return None
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))