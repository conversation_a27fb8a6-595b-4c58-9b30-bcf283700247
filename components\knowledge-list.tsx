"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { CheckCircle2, ChevronDown, ChevronUp, ThumbsUp, Star, Loader, LayoutGrid, LayoutList, Calendar, Eye, Search } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import { useDebounce } from "@/hooks/use-debounce"
import { KbDetailsDialog } from "@/components/kb-details-dialog"
import { KnowledgeEntry } from "@/types/knowledge"
import { likeKnowledgeEntry } from "@/lib/api"


interface KnowledgeListProps {
  filter: "all" | "verified" | "community" | "my-contributions" | "favorites"
  category: "all" | "solutions" | "guides" | "faq"
  projectId: number // Make projectId required and not undefined
  searchQuery: string
  onSearchChange?: (query: string) => void // Add callback for search changes
  additionalFilters?: {
    systems?: {
      oms?: boolean;
      wms?: boolean;
      automation?: boolean;
      other?: boolean;
    };
    types?: {
      database?: boolean;
      api?: boolean;
      ui?: boolean;
      workflow?: boolean;
      network?: boolean;
      other?: boolean;
    };
    verification?: string;
    sortBy?: string;
  }
  refreshTrigger?: number // A number that changes to trigger a refresh
}

export function KnowledgeList({
  filter,
  category,
  projectId,
  searchQuery,
  onSearchChange,
  additionalFilters,
  refreshTrigger = 0
}: KnowledgeListProps) {
  const [expandedEntry, setExpandedEntry] = useState<number | null>(null)
  // State for tracking favorites with proper typing
  const [favorites, setFavorites] = useState<number[]>([])
  const [isToggling, setIsToggling] = useState<Record<number, boolean>>({})
  const [knowledgeEntries, setKnowledgeEntries] = useState<KnowledgeEntry[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<"list" | "card">("card")
  const [selectedEntry, setSelectedEntry] = useState<KnowledgeEntry | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState<boolean>(false)
  // Local search state for the input field
  const [localSearchQuery, setLocalSearchQuery] = useState<string>(searchQuery || "")
  // Debounce the local search query to avoid excessive API calls
  const debouncedLocalSearchQuery = useDebounce(localSearchQuery, 300)
  const { toast } = useToast()

  // Sync local search state with prop changes
  useEffect(() => {
    setLocalSearchQuery(searchQuery || "")
  }, [searchQuery])

  // Handle debounced search query changes
  useEffect(() => {
    if (onSearchChange && debouncedLocalSearchQuery !== searchQuery) {
      onSearchChange(debouncedLocalSearchQuery)
    }
  }, [debouncedLocalSearchQuery, onSearchChange, searchQuery])

  // Load favorites from localStorage only (no API calls)
  useEffect(() => {
    const loadFavorites = () => {
      if (!projectId) return;

      try {
        // Get favorites from localStorage with project-specific key
        const storageKey = `knowledgeFavorites_project_${projectId}`;
        const storedFavorites = localStorage.getItem(storageKey);
        if (storedFavorites) {
          const parsedFavorites = JSON.parse(storedFavorites);
          if (Array.isArray(parsedFavorites)) {
            setFavorites(parsedFavorites);
          }
        } else {
          // Initialize empty favorites array for this project
          setFavorites([]);
        }
      } catch (error) {
        console.error("Error loading favorites from localStorage:", error);
        setFavorites([]);
      }
    };

    loadFavorites();
  }, [projectId, refreshTrigger]);

  // Function to fetch knowledge entries
  const fetchKnowledgeEntries = async (): Promise<void> => {
    setLoading(true);
    try {
      // Build the query URL with all filters
      // For favorites, we fetch all entries and filter client-side since favorites are stored in localStorage
      const apiFilter = filter === 'favorites' ? 'all' : filter;
      const queryParams = new URLSearchParams({
        project_id: projectId.toString(),
        filter: apiFilter,
        category: category,
        search: searchQuery
      });
  
      if (additionalFilters?.systems) {
        queryParams.append('systems', JSON.stringify(additionalFilters.systems));
      }
      if (additionalFilters?.types) {
        queryParams.append('types', JSON.stringify(additionalFilters.types));
      }
      if (additionalFilters?.verification) {
        queryParams.append('verification', additionalFilters.verification);
      }
      if (additionalFilters?.sortBy) {
        queryParams.append('sort_by', additionalFilters.sortBy);
      }
  
      const response = await fetch(`/api/knowledge-base?${queryParams}`);
      const data = await response.json();
      
      // Handle both array response and {items: []} response formats
      let entries = Array.isArray(data) ? data : data.items || [];
      
      // If we're in the favorites tab, filter the entries to only show favorites
      if (filter === 'favorites') {
        entries = entries.filter((entry: KnowledgeEntry) =>
          favorites.includes(entry.kb_id as number)
        );
      }
      
      setKnowledgeEntries(entries);
    } catch (err) {
      setError("Failed to load knowledge entries. Please try again later.");
      toast({
        title: "Error",
        description: "Failed to load knowledge entries",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch knowledge entries from the API when dependencies change
  useEffect(() => {
    fetchKnowledgeEntries()
  }, [filter, category, projectId, searchQuery, additionalFilters, refreshTrigger, favorites]) // Added favorites dependency back for proper filtering

  // Public method to refresh the list
  const refreshList = () => {
    fetchKnowledgeEntries()
  }

  // Handle search input changes
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value
    setLocalSearchQuery(newQuery)
    // The debounced effect will handle calling onSearchChange
  }

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (onSearchChange) {
      onSearchChange(localSearchQuery)
    }
  }

  const toggleExpand = (id: number) => {
    setExpandedEntry(expandedEntry === id ? null : id)
  }

  // Toggle favorite status using localStorage only
  const handleToggleFavorite = (id: number, e: React.MouseEvent) => {
    e.stopPropagation()

    // Prevent multiple rapid toggles
    if (isToggling[id]) return

    const isFavorite = favorites.includes(id)
    const newFavorites = isFavorite
      ? favorites.filter(favId => favId !== id)
      : [...favorites, id]

    // Update local state immediately for responsive UI
    setFavorites(newFavorites)
    setIsToggling(prev => ({ ...prev, [id]: true }))

    try {
      // Update localStorage for persistence with project-specific key
      const storageKey = `knowledgeFavorites_project_${projectId}`;
      localStorage.setItem(storageKey, JSON.stringify(newFavorites))

      // Show success feedback
      toast({
        title: isFavorite ? "Removed from favorites" : "Added to favorites",
        description: `Knowledge entry has been ${isFavorite ? 'removed from' : 'added to'} your favorites.`,
      })

      // Refresh the list if we're on the favorites tab
      if (filter === "favorites") {
        fetchKnowledgeEntries()
      }
    } catch (error) {
      console.error("Error updating favorites in localStorage:", error)

      // Revert UI on error
      setFavorites(prev => isFavorite ? [...prev, id] : prev.filter(favId => favId !== id))

      // Show error
      toast({
        title: "Error",
        description: "Failed to update favorites. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsToggling(prev => ({ ...prev, [id]: false }))
    }
  };

  const handleLike = async (id: number) => {
    try {
      await likeKnowledgeEntry(id)

      // Update local state
      setKnowledgeEntries(entries =>
        entries.map(entry =>
          entry.kb_id === id
            ? { ...entry, likes: (entry.likes || 0) + 1 }
            : entry
        )
      )

      toast({
        title: "Success",
        description: "You marked this entry as helpful",
      })
    } catch (err) {
      console.error("Failed to like entry:", err)
      toast({
        title: "Error",
        description: "Failed to mark as helpful",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader/>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-4">
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2 w-full max-w-md">
              <form onSubmit={handleSearchSubmit} className="relative w-full">
                <input
                  type="text"
                  placeholder="Search knowledge entries..."
                  className="w-full px-3 py-2 border rounded-md pl-10 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  value={localSearchQuery}
                  onChange={handleSearchInputChange}
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </form>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-8 w-8 p-0"
              >
                <LayoutList className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "card" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("card")}
                className="h-8 w-8 p-0"
              >
                <LayoutGrid className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshList}
          >
            Refresh
          </Button>
          </div>
    
          {knowledgeEntries.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No knowledge entries found for project ID {projectId}.</p>
              <p className="text-xs text-muted-foreground mt-2">Try changing filters or adding new entries.</p>
            </div>
          ) : viewMode === "card" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {knowledgeEntries.map((entry) => (
                <Card
                  key={entry.kb_id}
                  className="overflow-hidden h-full flex flex-col hover:shadow-md transition-shadow cursor-pointer border border-transparent hover:border-primary/20"
                  onClick={() => {
                    setSelectedEntry(entry)
                    setIsDetailsDialogOpen(true)
                  }}
                >
                  <CardContent className="p-4 flex-grow">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium line-clamp-1">{entry.title}</h3>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleFavorite(entry.kb_id, e);
                        }}
                        className="h-7 w-7"
                      >
                        <Star
                          className={cn(
                            "h-4 w-4 transition-colors",
                            favorites.includes(entry.kb_id) 
                              ? "fill-yellow-400 text-yellow-400" 
                              : "text-muted-foreground hover:text-yellow-400",
                            isToggling[entry.kb_id] && "opacity-50"
                          )}
                        />
                      </Button>
                    </div>
    
                    <div className="flex flex-wrap gap-1 mt-2 mb-2">
                      <Badge variant="outline" className="text-xs">{entry.system || "General"}</Badge>
                      <Badge variant="outline" className="text-xs">{entry.type || "Documentation"}</Badge>
                      {entry.verified && <Badge className="bg-green-500 hover:bg-green-600 text-white text-xs">Verified</Badge>}
                    </div>
    
                    <p className="text-xs line-clamp-3 text-muted-foreground mt-2">
                      {entry.content}
                    </p>
                  </CardContent>
    
                  <CardFooter className="pt-0 pb-3 flex justify-between items-center text-xs text-muted-foreground mt-auto">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(entry.created_at)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="flex items-center gap-1">
                        <ThumbsUp className="h-3 w-3" /> {entry.likes || 0}
                      </span>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {knowledgeEntries.map((entry) => (
                <Collapsible
                  key={entry.kb_id}
                  open={expandedEntry === entry.kb_id}
                  onOpenChange={() => toggleExpand(entry.kb_id)}
                >
                  <Card className="overflow-hidden hover:shadow-sm transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{entry.title}</h3>
                            {entry.verified && <Badge className="bg-green-500 hover:bg-green-600">Verified</Badge>}
                          </div>
                          <div className="mt-1 flex flex-wrap gap-2 text-xs text-muted-foreground">
                            <span className="inline-flex items-center rounded-full border px-2 py-0.5">{entry.system || "General"}</span>
                            <span className="inline-flex items-center rounded-full border px-2 py-0.5">{entry.type || "Documentation"}</span>
                            <span className="inline-flex items-center gap-1">
                              <ThumbsUp className="h-3 w-3" /> {entry.likes || 0}
                            </span>
                            <span className="inline-flex items-center gap-1">{entry.views || 0} views</span>
                            <span className="ml-auto">{formatDate(entry.created_at)}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 p-0"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleToggleFavorite(entry.kb_id, e);
                                  }}
                                >
                                  <Star
                                    className={cn(
                                      "h-4 w-4 transition-colors",
                                      favorites.includes(entry.kb_id) 
                                        ? "fill-yellow-400 text-yellow-400" 
                                        : "text-muted-foreground hover:text-yellow-400",
                                      isToggling[entry.kb_id] && "opacity-50"
                                    )}
                                  />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                {favorites.includes(entry.kb_id) ? "Remove from favorites" : "Add to favorites"}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={() => {
                                    setSelectedEntry(entry)
                                    setIsDetailsDialogOpen(true)
                                  }}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                View details
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <CollapsibleTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              {expandedEntry === entry.kb_id ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )}
                            </Button>
                          </CollapsibleTrigger>
                        </div>
                      </div>
                    </CardContent>
                    <CollapsibleContent>
                      <CardContent className="border-t px-4 py-3 text-sm">
                        <div>
                          <h4 className="font-medium">Content</h4>
                          <div className="mt-2 whitespace-pre-line text-muted-foreground">{entry.content}</div>
                        </div>
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Contributed by: {entry.author_name || `User #${entry.uploaded_by}`}</span>
                          {entry.verified && (
                            <span className="flex items-center gap-1">
                              <CheckCircle2 className="h-3 w-3 text-green-500" /> Verified solution
                            </span>
                          )}
                        </div>
                      </CardContent>
                    <CardFooter className="border-t px-4 py-3">
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleLike(entry.kb_id)}
                        >
                          <ThumbsUp className="mr-1 h-4 w-4" /> Helpful
                        </Button>
                        <Button size="sm" variant="outline">
                          Share
                        </Button>
                        {!entry.verified && (
                          <Button size="sm" variant="outline">
                            Suggest Edit
                          </Button>
                        )}
                      </div>
                    </CardFooter>
                  </CollapsibleContent>
                </Card>
              </Collapsible>
            ))}
          </div>
        )}
      </div>
      <KbDetailsDialog
        knowledgeEntry={selectedEntry}
        open={isDetailsDialogOpen}
        onOpenChange={setIsDetailsDialogOpen}
        onToggleFavorite={(knowledgeId) => {
          handleToggleFavorite(knowledgeId, {} as React.MouseEvent)
          if (selectedEntry && selectedEntry.kb_id === knowledgeId) {
            setSelectedEntry({
              ...selectedEntry,
              is_favorite: !favorites.includes(knowledgeId)
            })
          }
        }}
      />
    </>
  )
}