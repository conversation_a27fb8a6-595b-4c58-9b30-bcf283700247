import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { KnowledgeBase } from "@/lib/api/types";
import { knowledgeBaseApi } from "@/lib/api/knowledgeBase";

export function LatestKnowledgeEntries() {
  const [entries, setEntries] = useState<KnowledgeBase[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    knowledgeBaseApi
      .getKnowledgeBase({ limit: 5, sort_by: "created_at", sort_order: "desc" })
      .then((res) => {
        setEntries(res.items || []);
        setLoading(false);
      })
      .catch((err) => {
        setError("Failed to load latest knowledge entries.");
        setLoading(false);
      });
  }, []);

  if (loading) {
    return <div className="p-4 text-muted-foreground">Loading latest knowledge entries...</div>;
  }
  if (error) {
    return <div className="p-4 text-red-500">{error}</div>;
  }
  if (!entries.length) {
    return <div className="p-4 text-muted-foreground">No knowledge entries found.</div>;
  }

  return (
    <div className="space-y-3">
      {entries.map((entry) => (
        <Card key={entry.kb_id} className="overflow-hidden">
          <CardContent className="p-3">
            <div className="flex flex-col gap-2">
              <div className="flex items-start justify-between">
                <h3 className="font-medium flex items-center gap-1">
                  {entry.title}
                </h3>
                <span className="text-xs text-muted-foreground">{new Date(entry.created_at).toLocaleDateString()}</span>
              </div>
              <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                <span>By {entry.author_name || "Unknown"}</span>
                {entry.system && <span className="inline-flex items-center rounded-full border px-2 py-0.5">{entry.system}</span>}
                {entry.type && <span className="inline-flex items-center rounded-full border px-2 py-0.5">{entry.type}</span>}
              </div>
              <div className="text-sm line-clamp-2 text-ellipsis text-muted-foreground">
                {entry.content.slice(0, 100)}{entry.content.length > 100 ? "..." : ""}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
} 